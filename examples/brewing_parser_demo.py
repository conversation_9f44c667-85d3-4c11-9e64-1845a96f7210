#!/usr/bin/env python3
"""
Demonstration of the Brewing Report Parser

This script shows how to use the brewing report parser and demonstrates
the iterative development approach using debug mode.
"""

from examples.brewing_report_parser import parse_brewing_report, create_brewing_report_parser
import os


def demo_debug_mode():
    """Demonstrate debug mode functionality."""
    print("=" * 60)
    print("DEBUGGING DEMONSTRATION")
    print("=" * 60)
    print()
    print("This demonstrates how to use debug=True to understand parsing failures")
    print("and iteratively improve the parser.")
    print()
    
    # Create a sample text that would fail parsing
    sample_text = """================================================================================
                    BREWING EQUIPMENT PERFORMANCE ANALYSIS REPORT
                              Version 3.0.0 - Build 20250101
================================================================================

Report_Number:       BEP-2025-01-01-001
Creation_Time:       2025-01-01 12:00:00 UTC
Technician_Name:     Test User
Lab_Location:        Test Lab
Equipment_Model:     BrewMaster Pro 4000 Series
Analysis_Duration:   2025-01-01 to 2025-01-15
Total_Batch_Count:   25
Data_Record_Count:   8,500
Quality_Gate_Min:    >= 97.0%
System_Status:       TESTING

Rest of the file content...
"""
    
    # Save to a temporary file
    temp_file = "temp_test.txt"
    with open(temp_file, 'w') as f:
        f.write(sample_text)
    
    try:
        print("Attempting to parse a file with slightly different field names...")
        print("(This will likely fail with the current parser)")
        print()
        
        result = parse_brewing_report(temp_file, debug=False)
        if result:
            print("✓ Parsing succeeded!")
            print(f"  Report ID: {result.header.report_id}")
        else:
            print("✗ Parsing failed as expected")
            print()
            print("Now trying with debug=True to see detailed error information:")
            print("-" * 50)
            parse_brewing_report(temp_file, debug=True)
            
    finally:
        # Clean up
        if os.path.exists(temp_file):
            os.remove(temp_file)


def demo_successful_parsing():
    """Show successful parsing of all sample files."""
    print("\n" + "=" * 60)
    print("SUCCESSFUL PARSING DEMONSTRATION")
    print("=" * 60)
    print()
    
    sample_dir = "examples/sample_data"
    if not os.path.exists(sample_dir):
        print(f"Sample data directory not found: {sample_dir}")
        return
    
    sample_files = [f for f in os.listdir(sample_dir) if f.endswith('.txt')]
    sample_files.sort()
    
    print(f"Found {len(sample_files)} sample files to parse:")
    
    for filename in sample_files:
        file_path = os.path.join(sample_dir, filename)
        result = parse_brewing_report(file_path, debug=False)
        
        if result:
            print(f"✓ {filename}: {result.header.report_id} - {result.header.operator}")
        else:
            print(f"✗ {filename}: Failed to parse")


def demo_dataclass_benefits():
    """Demonstrate the benefits of using dataclass parsers."""
    print("\n" + "=" * 60)
    print("DATACLASS PARSER BENEFITS")
    print("=" * 60)
    print()
    
    print("Benefits of using Parmancer dataclass parsers:")
    print()
    print("1. TYPE SAFETY: Each field has a specific type (str, int, etc.)")
    print("2. STRUCTURE: Data is organized in meaningful dataclass objects")
    print("3. HARMONIZATION: Different field names map to consistent attributes")
    print("4. VALIDATION: Type conversion happens automatically (e.g., str -> int)")
    print("5. MAINTAINABILITY: Easy to extend with new fields or variations")
    print()
    
    # Parse one file to demonstrate
    sample_file = "examples/sample_data/0.txt"
    if os.path.exists(sample_file):
        result = parse_brewing_report(sample_file, debug=False)
        if result:
            print("Example parsed data structure:")
            print(f"  Type: {type(result.header)}")
            print(f"  Report ID (str): {result.header.report_id!r}")
            print(f"  Total Batches (int): {result.header.total_batches} (type: {type(result.header.total_batches)})")
            print(f"  Data Points (int): {result.header.data_points:,} (type: {type(result.header.data_points)})")
            print()
            print("This structured data can now be easily used for:")
            print("- Database insertion")
            print("- JSON/CSV export") 
            print("- Data analysis")
            print("- Report generation")


def demo_extension_ideas():
    """Show ideas for extending the parser."""
    print("\n" + "=" * 60)
    print("EXTENSION IDEAS")
    print("=" * 60)
    print()
    
    print("To extend this parser further, you could add:")
    print()
    print("1. SECTION PARSERS:")
    print("   - Mash tun analysis (temperature control, pH measurements)")
    print("   - Fermentation data (vessel performance, yeast analysis)")
    print("   - Quality metrics (analytical results, microbiological tests)")
    print("   - Filtration system data (filter stages, efficiency)")
    print()
    print("2. DATA VALIDATION:")
    print("   - Range checks (temperature, pressure, pH)")
    print("   - Unit conversion (Celsius/Fahrenheit, PSI/bar)")
    print("   - Date parsing and normalization")
    print()
    print("3. OUTPUT FORMATS:")
    print("   - JSON export")
    print("   - CSV/Excel export")
    print("   - Database models")
    print("   - Summary reports")
    print()
    print("4. ERROR HANDLING:")
    print("   - Partial parsing (continue on errors)")
    print("   - Missing field defaults")
    print("   - Format validation")
    print()
    print("The current parser demonstrates the foundation - structured parsing")
    print("with dataclass parsers and debug mode for iterative development.")


if __name__ == "__main__":
    print("Brewing Equipment Report Parser - Demonstration")
    print()
    
    demo_successful_parsing()
    demo_dataclass_benefits()
    demo_debug_mode()
    demo_extension_ideas()
    
    print("\n" + "=" * 60)
    print("DEMONSTRATION COMPLETE")
    print("=" * 60)
    print()
    print("Key takeaways:")
    print("- Parmancer dataclass parsers provide structure and type safety")
    print("- Debug mode helps iteratively improve parsers")
    print("- Field harmonization handles format variations")
    print("- The parser can be extended to handle more complex sections")
