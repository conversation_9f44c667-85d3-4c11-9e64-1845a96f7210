"""
Brewing Equipment Performance Analysis Report Parser

This parser handles brewing equipment reports with various formatting variations,
using Parmancer dataclass parsers to extract structured data.
"""

from dataclasses import dataclass
from typing import List, Optional, Union
from datetime import datetime
import re

from parmancer import (
    gather, regex, string, take, one_of, seq,
    ParseError, padding, whitespace, any_char
)


# Helper function to find patterns anywhere in text
def find_pattern(pattern: str, flags: int = re.IGNORECASE) -> any_char:
    """Create a parser that finds a pattern anywhere in the remaining text."""
    return regex(rf".*?{pattern}", flags=flags | re.DOTALL, group=1)


@dataclass
class ReportHeader:
    """Header information common to all brewing reports."""
    version: str = take(find_pattern(r"Version\s+([^\s]+(?:\s+[^\s]+)*?)\s+-\s+Build"))
    report_id: str = take(one_of(
        find_pattern(r"Report\s*ID:\s*([^\n]+)"),
        find_pattern(r"Report\s*Number:\s*([^\n]+)"),
        find_pattern(r"ReportID:\s*([^\n]+)"),
        find_pattern(r"Report_ID:\s*([^\n]+)")
    ).map(lambda s: s.strip()))

    generated_date: str = take(one_of(
        find_pattern(r"Generated:\s*([^\n]+)"),
        find_pattern(r"Creation\s*Date:\s*([^\n]+)"),
        find_pattern(r"Timestamp:\s*([^\n]+)"),
        find_pattern(r"Date_Generated:\s*([^\n]+)")
    ).map(lambda s: s.strip()))

    operator: str = take(one_of(
        find_pattern(r"Operator:\s*([^\n]+)"),
        find_pattern(r"Technician:\s*([^\n]+)"),
        find_pattern(r"User:\s*([^\n]+)"),
        find_pattern(r"Operator_Name:\s*([^\n]+)")
    ).map(lambda s: s.strip()))

    laboratory: str = take(one_of(
        find_pattern(r"Laboratory:\s*([^\n]+)"),
        find_pattern(r"Facility:\s*([^\n]+)"),
        find_pattern(r"Lab:\s*([^\n]+)"),
        find_pattern(r"Laboratory_Location:\s*([^\n]+)")
    ).map(lambda s: s.strip()))

    equipment: str = take(one_of(
        find_pattern(r"Equipment\s*Suite:\s*([^\n]+)"),
        find_pattern(r"Equipment:\s*([^\n]+)"),
        find_pattern(r"Equipment\s*Model:\s*([^\n]+)"),
        find_pattern(r"Equipment_Type:\s*([^\n]+)")
    ).map(lambda s: s.strip()))

    analysis_period: str = take(one_of(
        find_pattern(r"Analysis\s*Period:\s*([^\n]+)"),
        find_pattern(r"Analysis\s*Window:\s*([^\n]+)"),
        find_pattern(r"Period:\s*([^\n]+)"),
        find_pattern(r"Analysis_Timeframe:\s*([^\n]+)")
    ).map(lambda s: s.strip()))

    total_batches: int = take(one_of(
        find_pattern(r"Total\s*Batches:\s*(\d+)"),
        find_pattern(r"Batch\s*Count:\s*(\d+)"),
        find_pattern(r"Batch_Total:\s*(\d+)")
    ).map(int))

    data_points: int = take(one_of(
        find_pattern(r"Data\s*Points:\s*([\d,]+)"),
        find_pattern(r"Data\s*Records:\s*([\d,]+)"),
        find_pattern(r"Data_Count:\s*([\d,]+)")
    ).map(lambda s: int(s.replace(',', ''))))

    quality_threshold: str = take(one_of(
        find_pattern(r"Quality\s*Threshold:\s*([^\n]+)"),
        find_pattern(r"QC\s*Threshold:\s*([^\n]+)"),
        find_pattern(r"Quality\s*Gate:\s*([^\n]+)"),
        find_pattern(r"Quality_Minimum:\s*([^\n]+)")
    ).map(lambda s: s.strip()))

    status: str = take(one_of(
        find_pattern(r"Status:\s*([^\n]+)"),
        find_pattern(r"System\s*State:\s*([^\n]+)"),
        find_pattern(r"Operational_Status:\s*([^\n]+)")
    ).map(lambda s: s.strip()))


@dataclass
class BatchTemperatureData:
    """Temperature control data for individual batches."""
    batch_id: str = take(regex(r"(BT-\d+)"))
    target_temp: float = take(regex(r"[\d.]+").map(float))
    actual_temp: float = take(regex(r"[\d.]+").map(float))
    deviation: float = take(regex(r"[+-]?[\d.]+").map(float))
    hold_time: int = take(regex(r"\d+").map(int))
    efficiency: float = take(regex(r"[\d.]+").map(float))


@dataclass
class PHMeasurement:
    """pH measurement data point."""
    time_min: int = take(regex(r"\d+").map(int))
    ph_reading: float = take(regex(r"[\d.]+").map(float))
    buffer_capacity: float = take(regex(r"[\d.]+").map(float))
    drift_rate: float = take(regex(r"[+-]?[\d.]+").map(float))
    status: str = take(regex(r"\w+"))


@dataclass
class MashTunAnalysis:
    """Mash tun analysis section data."""
    temperature_batches: List[BatchTemperatureData] = take(
        # Skip headers and parse batch data
        regex(r"Temperature.*?:\n.*?\n.*?\n") >>
        gather(BatchTemperatureData).sep_by(string("\n"))
    )
    avg_temp_deviation: str = take(
        regex(r"(?:Average|Mean)\s*Temperature.*?:\s*([^\n]+)", flags=re.IGNORECASE, group=1).map(lambda s: s.strip())
    )
    control_efficiency: str = take(
        regex(r"(?:Temperature\s*Control|Control\s*System)\s*Efficiency:\s*([^\n]+)", flags=re.IGNORECASE, group=1).map(lambda s: s.strip())
    )
    recommended_action: str = take(
        regex(r"(?:Recommended\s*Action|Action\s*Required):\s*([^\n]+)", flags=re.IGNORECASE, group=1).map(lambda s: s.strip())
    )
    ph_measurements: List[PHMeasurement] = take(
        # Skip pH headers and parse measurements
        regex(r"pH.*?:\n.*?\n.*?\n") >>
        gather(PHMeasurement).sep_by(string("\n"))
    )
    ph_stability: str = take(
        regex(r"pH\s*(?:Stability|Drift\s*Rate).*?:\s*([^\n]+)", flags=re.IGNORECASE, group=1).map(lambda s: s.strip())
    )


@dataclass
class VesselData:
    """Fermentation vessel performance data."""
    vessel_id: str = take(regex(r"FV-[A-Z]\d+"))
    volume: int = take(regex(r"\d+").map(int))
    pressure: float = take(regex(r"[\d.]+").map(float))
    co2_rate: float = take(regex(r"[\d.]+").map(float))
    temperature: float = take(regex(r"[\d.]+").map(float))
    gravity: float = take(regex(r"[\d.]+").map(float))
    status: str = take(regex(r"\w+"))


@dataclass
class YeastSample:
    """Yeast viability assessment data."""
    sample_id: str = take(regex(r"YS-\d+"))
    cell_count: str = take(regex(r"[\d.]+e\d+"))  # Scientific notation
    viability: float = take(regex(r"[\d.]+").map(float))
    budding: float = take(regex(r"[\d.]+").map(float))
    contamination: str = take(regex(r"[<\d.%]+"))
    grade: str = take(regex(r"[A-Z][+]?"))


@dataclass
class FermentationData:
    """Fermentation vessel and yeast analysis data."""
    vessels: List[VesselData] = take(
        regex(r"Vessel.*?:\n.*?\n.*?\n") >>
        gather(VesselData).sep_by(string("\n"))
    )
    yeast_samples: List[YeastSample] = take(
        regex(r"Yeast.*?:\n.*?\n.*?\n") >>
        gather(YeastSample).sep_by(string("\n"))
    )
    avg_yeast_viability: str = take(
        regex(r"(?:Average|Mean)\s*Yeast\s*Viability.*?:\s*([^\n]+)", flags=re.IGNORECASE, group=1).map(lambda s: s.strip())
    )


@dataclass
class QualityMetric:
    """Individual quality measurement."""
    parameter: str = take(regex(r"[^:]+"))
    target_range: str = take(regex(r"[^:]+"))
    measured_value: str = take(regex(r"[^:]+"))
    deviation: str = take(regex(r"[^:]+"))
    status: str = take(regex(r"\w+"))


@dataclass
class MicrobiologicalTest:
    """Microbiological test result."""
    test_type: str = take(regex(r"[^:]+"))
    result: str = take(regex(r"[^:]+"))
    specification: str = take(regex(r"[^:]+"))
    status: str = take(regex(r"\w+"))


@dataclass
class QualitySection:
    """Quality metrics and microbiological analysis."""
    quality_metrics: List[QualityMetric] = take(
        regex(r"(?:Analytical|Final\s*Product|Product\s*Quality).*?:\n.*?\n.*?\n") >>
        gather(QualityMetric).sep_by(string("\n"))
    )
    microbiological_tests: List[MicrobiologicalTest] = take(
        regex(r"(?:Microbiological|Contamination).*?:\n.*?\n.*?\n") >>
        gather(MicrobiologicalTest).sep_by(string("\n"))
    )


@dataclass
class FilterStage:
    """Individual filtration stage data."""
    stage: int = take(regex(r"\d+").map(int))
    filter_type: str = take(regex(r"\w+"))
    pore_size: str = take(regex(r"[\d.]+μm"))
    flow_rate: str = take(regex(r"[\d.]+\s*L/h"))
    pressure_drop: str = take(regex(r"[\d.]+\s*PSI"))
    efficiency: str = take(regex(r"[\d.]+%"))
    hours: int = take(regex(r"\d+").map(int))


@dataclass
class FiltrationData:
    """Filtration system performance data (optional section)."""
    filter_stages: List[FilterStage] = take(
        regex(r"Filter.*?:\n.*?\n.*?\n") >>
        gather(FilterStage).sep_by(string("\n"))
    )
    total_efficiency: str = take(
        regex(r"(?:Total\s*System|Overall)\s*Efficiency:\s*([^\n]+)", flags=re.IGNORECASE, group=1).map(lambda s: s.strip())
    )
    replacement_recommendation: str = take(
        regex(r"(?:Recommended|Filter\s*Replacement).*?:\s*([^\n]+)", flags=re.IGNORECASE, group=1).map(lambda s: s.strip())
    )


@dataclass
class BrewingReport:
    """Complete brewing equipment performance analysis report."""
    header: ReportHeader = take(gather(ReportHeader))
    mash_tun: MashTunAnalysis = take(gather(MashTunAnalysis))
    fermentation: FermentationData = take(gather(FermentationData))
    quality: QualitySection = take(gather(QualitySection))
    filtration: Optional[FiltrationData] = take(gather(FiltrationData).optional())


def create_brewing_report_parser():
    """Create the main brewing report parser."""
    return gather(BrewingReport)


def parse_brewing_report(file_path: str, debug: bool = False) -> Union[BrewingReport, None]:
    """
    Parse a brewing report file and return the structured data.

    Args:
        file_path: Path to the brewing report file
        debug: Enable debug mode for detailed error information

    Returns:
        BrewingReport object if successful, None if parsing fails
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        parser = create_brewing_report_parser()
        result = parser.parse(content, debug=debug)
        return result

    except ParseError as e:
        print(f"Parse error in {file_path}:")
        print(str(e))
        return None
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None


def test_all_sample_files():
    """Test the parser on all sample files with debug information."""
    import os

    sample_dir = "examples/sample_data"
    sample_files = [f for f in os.listdir(sample_dir) if f.endswith('.txt')]
    sample_files.sort()

    print("Testing Brewing Report Parser")
    print("=" * 50)

    for filename in sample_files:
        file_path = os.path.join(sample_dir, filename)
        print(f"\nTesting {filename}:")
        print("-" * 30)

        # First try without debug to see if it succeeds
        result = parse_brewing_report(file_path, debug=False)

        if result:
            print(f"✓ Successfully parsed {filename}")
            print(f"  Report ID: {result.header.report_id}")
            print(f"  Operator: {result.header.operator}")
            print(f"  Total Batches: {result.header.total_batches}")
            print(f"  Status: {result.header.status}")
        else:
            print(f"✗ Failed to parse {filename}")
            print("Trying with debug mode...")
            # Try again with debug mode for detailed error info
            parse_brewing_report(file_path, debug=True)


if __name__ == "__main__":
    test_all_sample_files()
