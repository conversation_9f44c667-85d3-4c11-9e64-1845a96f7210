"""
Brewing Equipment Performance Analysis Report Parser

This parser handles brewing equipment reports with various formatting variations,
using Parmancer dataclass parsers to extract structured data.
"""

from dataclasses import dataclass
from typing import List, Optional, Union
import re
from pprint import pprint

from parmancer import (
    gather, regex, string, take, one_of, seq,
    ParseError, padding, whitespace
)


# Reusable parsers for common patterns
equals_line = regex(r"=+\n")  # Line of equals signs
any_line = regex(r"[^\n]*\n")  # Any line including newline
field_value = regex(r"([^\n]+)")  # Capture content until newline
number = regex(r"(\d+)").map(int)  # Integer number
number_with_commas = regex(r"([\d,]+)").map(lambda s: int(s.replace(',', '')))  # Number with commas


@dataclass
class ReportHeader:
    """Header information common to all brewing reports."""
    # Skip the initial equals line and title lines, then parse fields
    version: str = take(
        equals_line >>  # Skip first equals line
        any_line >>     # Skip title line
        regex(r"\s*Version\s+([^\s]+(?:\s+[^\s]+)*?)\s+-\s+Build[^\n]*\n", group=1)
    )

    report_id: str = take(
        equals_line >>  # Skip second equals line
        string("\n") >> # Skip empty line
        one_of(
            regex(r"Report\s+ID:\s*([^\n]+)\n", group=1),  # "Report ID:" with space
            regex(r"Report\s+Number:\s*([^\n]+)\n", group=1),  # "Report Number:" with space
            regex(r"ReportID:\s*([^\n]+)\n", group=1),  # "ReportID:" no space
            regex(r"Report_ID:\s*([^\n]+)\n", group=1)  # "Report_ID:" with underscore
        ).map(lambda s: s.strip())
    )

    generated_date: str = take(
        one_of(
            regex(r"Generated:\s*([^\n]+)\n", group=1),
            regex(r"Creation\s*Date:\s*([^\n]+)\n", group=1),
            regex(r"Timestamp:\s*([^\n]+)\n", group=1),
            regex(r"Date_Generated:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    operator: str = take(
        one_of(
            regex(r"Operator:\s*([^\n]+)\n", group=1),
            regex(r"Technician:\s*([^\n]+)\n", group=1),
            regex(r"User:\s*([^\n]+)\n", group=1),
            regex(r"Operator_Name:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    laboratory: str = take(
        one_of(
            regex(r"Laboratory:\s*([^\n]+)\n", group=1),
            regex(r"Facility:\s*([^\n]+)\n", group=1),
            regex(r"Lab:\s*([^\n]+)\n", group=1),
            regex(r"Laboratory_Location:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    equipment: str = take(
        one_of(
            regex(r"Equipment\s*Suite:\s*([^\n]+)\n", group=1),
            regex(r"Equipment:\s*([^\n]+)\n", group=1),
            regex(r"Equipment\s*Model:\s*([^\n]+)\n", group=1),
            regex(r"Equipment_Type:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    analysis_period: str = take(
        one_of(
            regex(r"Analysis\s*Period:\s*([^\n]+)\n", group=1),
            regex(r"Analysis\s*Window:\s*([^\n]+)\n", group=1),
            regex(r"Period:\s*([^\n]+)\n", group=1),
            regex(r"Analysis_Timeframe:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    total_batches: int = take(
        one_of(
            regex(r"Total\s*Batches:\s*(\d+)\n", group=1),
            regex(r"Batch\s*Count:\s*(\d+)\n", group=1),
            regex(r"Batch_Total:\s*(\d+)\n", group=1)
        ).map(int)
    )

    data_points: int = take(
        one_of(
            regex(r"Data\s*Points:\s*([\d,]+)\n", group=1),
            regex(r"Data\s*Records:\s*([\d,]+)\n", group=1),
            regex(r"Data_Count:\s*([\d,]+)\n", group=1)
        ).map(lambda s: int(s.replace(',', '')))
    )

    quality_threshold: str = take(
        one_of(
            regex(r"Quality\s*Threshold:\s*([^\n]+)\n", group=1),
            regex(r"QC\s*Threshold:\s*([^\n]+)\n", group=1),
            regex(r"Quality\s*Gate:\s*([^\n]+)\n", group=1),
            regex(r"Quality_Minimum:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    status: str = take(
        one_of(
            regex(r"Status:\s*([^\n]+)\n", group=1),
            regex(r"System\s*State:\s*([^\n]+)\n", group=1),
            regex(r"Operational_Status:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )


@dataclass
class BatchTemperatureData:
    """Temperature control data for individual batches."""
    batch_id: str = take(one_of(
        regex(r"(BT-\d+)"),
        regex(r"(Batch_\d+)"),
        regex(r"(B\d+)")
    ))
    target_temp: float = take(whitespace >> regex(r"[\d.]+").map(float))
    actual_temp: float = take(whitespace >> regex(r"[\d.]+").map(float))
    deviation: float = take(whitespace >> regex(r"[+-]?[\d.]+").map(float))
    hold_time: int = take(whitespace >> regex(r"\d+").map(int))
    efficiency: float = take(whitespace >> regex(r"[\d.]+").map(float) << string("\n"))


@dataclass
class MashTunAnalysis:
    """Mash tun analysis section data."""
    temperature_batches: List[BatchTemperatureData] = take(
        # Find the temperature control section - use more specific patterns
        regex(r".*?Temperature.*?(?:Performance|Control|Results).*?:\n", flags=re.DOTALL) >>
        # Skip header lines
        regex(r".*?\n.*?\n", flags=re.DOTALL) >>
        # Parse batch data lines
        gather(BatchTemperatureData).many()
    )


@dataclass
class VesselData:
    """Fermentation vessel performance data."""
    vessel_id: str = take(one_of(
        regex(r"FV-[A-Z]\d+"),
        regex(r"Tank_[A-Z]\d+"),
        regex(r"V\d+"),
        regex(r"T\d+")
    ))
    volume: int = take(whitespace >> regex(r"\d+").map(int))
    pressure: float = take(whitespace >> regex(r"[\d.]+").map(float))
    co2_rate: float = take(whitespace >> regex(r"[\d.]+").map(float))
    temperature: float = take(whitespace >> regex(r"[\d.]+").map(float))
    gravity: float = take(whitespace >> regex(r"[\d.]+").map(float))
    status: str = take(whitespace >> regex(r"\w+") << string("\n"))


@dataclass
class YeastSample:
    """Yeast viability assessment data."""
    sample_id: str = take(regex(r"YS-\d+"))
    cell_count: str = take(whitespace >> regex(r"[\d.]+e\d+"))
    viability: float = take(whitespace >> regex(r"[\d.]+").map(float))
    budding: float = take(whitespace >> regex(r"[\d.]+").map(float))
    contamination: str = take(whitespace >> regex(r"[<\d.%]+"))
    grade: str = take(whitespace >> regex(r"[A-Z][+]?") << string("\n"))


@dataclass
class FermentationData:
    """Fermentation vessel and yeast analysis data."""
    vessels: List[VesselData] = take(
        # Find vessel section - use a single pattern that matches all variations
        regex(r".*?(?:Vessel|Tank).*?(?:Status|Monitoring|Performance|Data).*?:\n", flags=re.DOTALL) >>
        # Skip header
        regex(r".*?\n.*?\n", flags=re.DOTALL) >>
        # Parse vessel data
        gather(VesselData).many()
    )
    yeast_samples: List[YeastSample] = take(
        # Find yeast section - use a single pattern that matches all variations
        regex(r".*?(?:Yeast|Microorganism|Culture).*?(?:Analysis|Assessment|Viability).*?:\n", flags=re.DOTALL) >>
        # Skip header
        regex(r".*?\n.*?\n", flags=re.DOTALL) >>
        # Parse yeast data
        gather(YeastSample).many()
    )


@dataclass
class BrewingReport:
    """Complete brewing equipment performance analysis report."""
    header: ReportHeader = take(gather(ReportHeader))
    mash_tun: MashTunAnalysis = take(gather(MashTunAnalysis))
    fermentation: FermentationData = take(gather(FermentationData))
    remaining_content: str = take(regex(r".*", flags=re.DOTALL))


def create_brewing_report_parser():
    """Create the main brewing report parser."""
    return gather(BrewingReport)


def parse_brewing_report(file_path: str, debug: bool = False) -> Union[BrewingReport, None]:
    """
    Parse a brewing report file and return the structured data.

    Args:
        file_path: Path to the brewing report file
        debug: Enable debug mode for detailed error information

    Returns:
        BrewingReport object if successful, None if parsing fails
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        parser = create_brewing_report_parser()
        result = parser.parse(content, debug=debug)
        return result

    except ParseError as e:
        print(f"Parse error in {file_path}:")
        print(str(e))
        return None
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None


def test_all_sample_files():
    """Test the parser on all sample files."""
    import os

    sample_dir = "examples/sample_data"
    sample_files = [f for f in os.listdir(sample_dir) if f.endswith('.txt')]
    sample_files.sort()

    results = []

    for filename in sample_files:
        file_path = os.path.join(sample_dir, filename)
        result = parse_brewing_report(file_path, debug=False)
        print(result)
        if result:
            results.append({
                'file': filename,
                'report_id': result.header.report_id,
                'operator': result.header.operator,
                'total_batches': result.header.total_batches,
                'status': result.header.status,
                'temp_batches_count': len(result.mash_tun.temperature_batches),
                'vessels_count': len(result.fermentation.vessels),
                'yeast_samples_count': len(result.fermentation.yeast_samples)
            })
        else:
            print(f"Failed to parse {filename}")
            parse_brewing_report(file_path, debug=True)

    if results:
        # Print as table
        headers = ['File', 'Report ID', 'Operator', 'Batches', 'Status', 'Temp Data', 'Vessels', 'Yeast Samples']
        print(f"{headers[0]:<8} {headers[1]:<20} {headers[2]:<15} {headers[3]:<8} {headers[4]:<12} {headers[5]:<10} {headers[6]:<8} {headers[7]:<12}")
        print("-" * 100)

        for r in results:
            print(f"{r['file']:<8} {r['report_id']:<20} {r['operator']:<15} {r['total_batches']:<8} "
                  f"{r['status']:<12} {r['temp_batches_count']:<10} {r['vessels_count']:<8} {r['yeast_samples_count']:<12}")

        return results
    return None


if __name__ == "__main__":
    test_all_sample_files()
