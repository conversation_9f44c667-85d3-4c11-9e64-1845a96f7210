"""
Brewing Equipment Performance Analysis Report Parser

This parser handles brewing equipment reports with various formatting variations,
using Parmancer dataclass parsers to extract structured data.
"""

from dataclasses import dataclass
from typing import List, Optional, Union
import re

from parmancer import (
    gather, regex, string, take, one_of, seq,
    ParseError, padding, whitespace
)


# Reusable parsers for common patterns
equals_line = regex(r"=+\n")  # Line of equals signs
any_line = regex(r"[^\n]*\n")  # Any line including newline
field_value = regex(r"([^\n]+)")  # Capture content until newline
number = regex(r"(\d+)").map(int)  # Integer number
number_with_commas = regex(r"([\d,]+)").map(lambda s: int(s.replace(',', '')))  # Number with commas


@dataclass
class ReportHeader:
    """Header information common to all brewing reports."""
    # Skip the initial equals line and title lines, then parse fields
    version: str = take(
        equals_line >>  # Skip first equals line
        any_line >>     # Skip title line
        regex(r"\s*Version\s+([^\s]+(?:\s+[^\s]+)*?)\s+-\s+Build[^\n]*\n", group=1)
    )

    report_id: str = take(
        equals_line >>  # Skip second equals line
        string("\n") >> # Skip empty line
        one_of(
            regex(r"Report\s+ID:\s*([^\n]+)\n", group=1),  # "Report ID:" with space
            regex(r"Report\s+Number:\s*([^\n]+)\n", group=1),  # "Report Number:" with space
            regex(r"ReportID:\s*([^\n]+)\n", group=1),  # "ReportID:" no space
            regex(r"Report_ID:\s*([^\n]+)\n", group=1)  # "Report_ID:" with underscore
        ).map(lambda s: s.strip())
    )

    generated_date: str = take(
        one_of(
            regex(r"Generated:\s*([^\n]+)\n", group=1),
            regex(r"Creation\s*Date:\s*([^\n]+)\n", group=1),
            regex(r"Timestamp:\s*([^\n]+)\n", group=1),
            regex(r"Date_Generated:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    operator: str = take(
        one_of(
            regex(r"Operator:\s*([^\n]+)\n", group=1),
            regex(r"Technician:\s*([^\n]+)\n", group=1),
            regex(r"User:\s*([^\n]+)\n", group=1),
            regex(r"Operator_Name:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    laboratory: str = take(
        one_of(
            regex(r"Laboratory:\s*([^\n]+)\n", group=1),
            regex(r"Facility:\s*([^\n]+)\n", group=1),
            regex(r"Lab:\s*([^\n]+)\n", group=1),
            regex(r"Laboratory_Location:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    equipment: str = take(
        one_of(
            regex(r"Equipment\s*Suite:\s*([^\n]+)\n", group=1),
            regex(r"Equipment:\s*([^\n]+)\n", group=1),
            regex(r"Equipment\s*Model:\s*([^\n]+)\n", group=1),
            regex(r"Equipment_Type:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    analysis_period: str = take(
        one_of(
            regex(r"Analysis\s*Period:\s*([^\n]+)\n", group=1),
            regex(r"Analysis\s*Window:\s*([^\n]+)\n", group=1),
            regex(r"Period:\s*([^\n]+)\n", group=1),
            regex(r"Analysis_Timeframe:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    total_batches: int = take(
        one_of(
            regex(r"Total\s*Batches:\s*(\d+)\n", group=1),
            regex(r"Batch\s*Count:\s*(\d+)\n", group=1),
            regex(r"Batch_Total:\s*(\d+)\n", group=1)
        ).map(int)
    )

    data_points: int = take(
        one_of(
            regex(r"Data\s*Points:\s*([\d,]+)\n", group=1),
            regex(r"Data\s*Records:\s*([\d,]+)\n", group=1),
            regex(r"Data_Count:\s*([\d,]+)\n", group=1)
        ).map(lambda s: int(s.replace(',', '')))
    )

    quality_threshold: str = take(
        one_of(
            regex(r"Quality\s*Threshold:\s*([^\n]+)\n", group=1),
            regex(r"QC\s*Threshold:\s*([^\n]+)\n", group=1),
            regex(r"Quality\s*Gate:\s*([^\n]+)\n", group=1),
            regex(r"Quality_Minimum:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )

    status: str = take(
        one_of(
            regex(r"Status:\s*([^\n]+)\n", group=1),
            regex(r"System\s*State:\s*([^\n]+)\n", group=1),
            regex(r"Operational_Status:\s*([^\n]+)\n", group=1)
        ).map(lambda s: s.strip())
    )


# For now, let's create a simple parser that just extracts the header
@dataclass
class SimpleBrewingReport:
    """Simplified brewing report that just parses the header for now."""
    header: ReportHeader = take(gather(ReportHeader))
    # Consume the rest of the file content for now
    remaining_content: str = take(regex(r".*", flags=re.DOTALL))


def create_brewing_report_parser():
    """Create the main brewing report parser."""
    return gather(SimpleBrewingReport)


def parse_brewing_report(file_path: str, debug: bool = False) -> Union[SimpleBrewingReport, None]:
    """
    Parse a brewing report file and return the structured data.

    Args:
        file_path: Path to the brewing report file
        debug: Enable debug mode for detailed error information

    Returns:
        SimpleBrewingReport object if successful, None if parsing fails
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        parser = create_brewing_report_parser()
        result = parser.parse(content, debug=debug)
        return result

    except ParseError as e:
        print(f"Parse error in {file_path}:")
        print(str(e))
        return None
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None


def test_all_sample_files():
    """Test the parser on all sample files with debug information."""
    import os

    sample_dir = "examples/sample_data"
    sample_files = [f for f in os.listdir(sample_dir) if f.endswith('.txt')]
    sample_files.sort()

    print("Testing Brewing Report Parser")
    print("=" * 50)

    results = []

    for filename in sample_files:
        file_path = os.path.join(sample_dir, filename)
        print(f"\nTesting {filename}:")
        print("-" * 30)

        # First try without debug to see if it succeeds
        result = parse_brewing_report(file_path, debug=False)

        if result:
            print(f"✓ Successfully parsed {filename}")
            print(f"  Report ID: {result.header.report_id}")
            print(f"  Version: {result.header.version}")
            print(f"  Generated: {result.header.generated_date}")
            print(f"  Operator: {result.header.operator}")
            print(f"  Laboratory: {result.header.laboratory}")
            print(f"  Equipment: {result.header.equipment}")
            print(f"  Analysis Period: {result.header.analysis_period}")
            print(f"  Total Batches: {result.header.total_batches}")
            print(f"  Data Points: {result.header.data_points:,}")
            print(f"  Quality Threshold: {result.header.quality_threshold}")
            print(f"  Status: {result.header.status}")
            print(f"  Remaining content length: {len(result.remaining_content)} characters")
            results.append((filename, result))
        else:
            print(f"✗ Failed to parse {filename}")
            print("Trying with debug mode...")
            # Try again with debug mode for detailed error info
            parse_brewing_report(file_path, debug=True)

    # Show harmonization summary
    if results:
        print("\n" + "=" * 70)
        print("HARMONIZATION SUMMARY")
        print("=" * 70)
        print("The parser successfully harmonized different field naming conventions:")
        print()

        print("Field Name Variations Successfully Handled:")
        print("- Report ID: 'Report ID:', 'Report Number:', 'ReportID:', 'Report_ID:'")
        print("- Date: 'Generated:', 'Creation Date:', 'Timestamp:', 'Date_Generated:'")
        print("- Operator: 'Operator:', 'Technician:', 'User:', 'Operator_Name:'")
        print("- Lab: 'Laboratory:', 'Facility:', 'Lab:', 'Laboratory_Location:'")
        print("- Equipment: 'Equipment Suite:', 'Equipment:', 'Equipment Model:', 'Equipment_Type:'")
        print("- Period: 'Analysis Period:', 'Analysis Window:', 'Period:', 'Analysis_Timeframe:'")
        print("- Batches: 'Total Batches:', 'Batch Count:', 'Batch_Total:'")
        print("- Data: 'Data Points:', 'Data Records:', 'Data_Count:'")
        print("- Quality: 'Quality Threshold:', 'QC Threshold:', 'Quality Gate:', 'Quality_Minimum:'")
        print("- Status: 'Status:', 'System State:', 'Operational_Status:'")
        print()

        print("Parsed Data Summary:")
        print(f"{'File':<8} {'Report ID':<20} {'Operator':<15} {'Batches':<8} {'Status':<12}")
        print("-" * 70)
        for filename, result in results:
            print(f"{filename:<8} {result.header.report_id:<20} {result.header.operator:<15} "
                  f"{result.header.total_batches:<8} {result.header.status:<12}")

        total_batches = sum(r.header.total_batches for _, r in results)
        total_data_points = sum(r.header.data_points for _, r in results)
        print("-" * 70)
        print(f"TOTALS: {len(results)} files, {total_batches} batches, {total_data_points:,} data points")


if __name__ == "__main__":
    test_all_sample_files()
